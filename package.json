{"name": "@rocket-logic-ensemble/source", "version": "0.0.0", "license": "MIT", "scripts": {"serve:ui": "nx serve ensemble-ui", "serve:api": "nx serve ensemble-bff", "serve": "nx run-many --target=serve --projects=ensemble-bff,ensemble-ui --parallel=true", "build:ui": "nx build ensemble-ui", "build:ensemble-ui": "nx build ensemble-ui", "build:bff": "nx build ensemble-bff", "build": "nx run-many --target=build --projects=ensemble-bff,ensemble-ui", "test:ui": "nx test ensemble-ui", "test:bff": "nx test ensemble-bff", "test": "nx run-many --target=test --projects=ensemble-bff,ensemble-ui", "e2e:ui": "nx e2e ensemble-ui-e2e", "e2e:bff": "nx e2e ensemble-bff-e2e", "e2e": "nx run-many --target=e2e --projects=ensemble-bff-e2e,ensemble-ui-e2e", "lint:ui": "nx lint ensemble-ui", "lint:bff": "nx lint ensemble-bff", "lint": "nx run-many --target=lint --projects=ensemble-bff,ensemble-ui", "affected:build": "nx affected:build", "affected:test": "nx affected:test", "affected:lint": "nx affected:lint", "format": "nx format:write", "format:check": "nx format:check", "graph": "nx graph", "build:lib:ui": "nx run ui:build", "build:lib:models": "nx run ui:models"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/cdk": "^18.2.14", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/material": "^18.2.14", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nx/nx-darwin-arm64": "^21.2.1", "@rocket-logic/rl-xp-bff-models": "2.47.2", "@rocketcentral/rocket-design-system-angular": "^9.0.0", "playwright": "^1.53.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.15", "@angular-devkit/core": "^18.2.15", "@angular/cli": "^18.2.15", "@angular/compiler-cli": "^18.2.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@nx/angular": "19.1.2", "@nx/eslint": "19.1.2", "@nx/jest": "19.1.2", "@nx/js": "19.1.2", "@nx/nest": "19.1.2", "@nx/playwright": "^19.1.2", "@nx/workspace": "19.1.2", "@playwright/test": "^1.40.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jasmine": "~5.1.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jasmine-core": "~5.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.2", "jest-preset-angular": "^14.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "nx": "19.1.2", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.5.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}