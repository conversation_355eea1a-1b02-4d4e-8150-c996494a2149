import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'filter',
  standalone: true
})
export class FilterPipe implements PipeTransform {
  transform<T>(array: T[], property: string, value: any): T[] {
    if (!array || !property) {
      return array;
    }
    
    return array.filter(item => {
      const itemValue = (item as any)[property];
      return itemValue === value;
    });
  }
}
