import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { interval, Subject, takeUntil } from 'rxjs';

export interface SystemStatus {
  name: string;
  status: 'operational' | 'degraded' | 'outage' | 'maintenance';
  description: string;
  lastUpdated: Date;
  responseTime?: number | null; // in ms
  uptime?: number; // percentage
}

@Component({
  selector: 'ens-system-status-carousel',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule
  ],
  templateUrl: './system-status-carousel.component.html',
  styleUrls: ['./system-status-carousel.component.scss']
})
export class SystemStatusCarouselComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  
  systems: SystemStatus[] = [
    {
      name: 'AMP',
      status: 'operational',
      description: 'Application Management Platform running normally',
      lastUpdated: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
      responseTime: 145,
      uptime: 99.9
    },
    {
      name: 'RLA<PERSON>',
      status: 'operational',
      description: 'Rocket Logic API responding normally',
      lastUpdated: new Date(Date.now() - 1 * 60 * 1000), // 1 minute ago
      responseTime: 89,
      uptime: 99.8
    },
    {
      name: 'LIS',
      status: 'degraded',
      description: 'Loan Information System experiencing slow response times',
      lastUpdated: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      responseTime: 2340,
      uptime: 98.2
    },
    {
      name: 'LOPA',
      status: 'operational',
      description: 'Loan Origination Platform running smoothly',
      lastUpdated: new Date(Date.now() - 3 * 60 * 1000), // 3 minutes ago
      responseTime: 234,
      uptime: 99.5
    },
    {
      name: 'OurHouse',
      status: 'maintenance',
      description: 'Scheduled maintenance in progress',
      lastUpdated: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
      responseTime: null,
      uptime: 95.0
    },
    {
      name: 'AWS Route53',
      status: 'operational',
      description: 'DNS services operating normally',
      lastUpdated: new Date(Date.now() - 1 * 60 * 1000), // 1 minute ago
      responseTime: 12,
      uptime: 100.0
    },
    {
      name: 'AWS EKS',
      status: 'operational',
      description: 'Kubernetes cluster healthy',
      lastUpdated: new Date(Date.now() - 4 * 60 * 1000), // 4 minutes ago
      responseTime: 67,
      uptime: 99.7
    }
  ];

  currentIndex = 0;
  itemsToShow = 3; // Number of items visible at once
  autoScrollInterval = 4000; // 4 seconds

  ngOnInit(): void {
    this.startAutoScroll();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private startAutoScroll(): void {
    interval(this.autoScrollInterval)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.nextSlide();
      });
  }

  nextSlide(): void {
    this.currentIndex = (this.currentIndex + 1) % this.systems.length;
  }

  previousSlide(): void {
    this.currentIndex = this.currentIndex === 0 ? this.systems.length - 1 : this.currentIndex - 1;
  }

  getVisibleSystems(): SystemStatus[] {
    const visible: SystemStatus[] = [];
    for (let i = 0; i < this.itemsToShow; i++) {
      const index = (this.currentIndex + i) % this.systems.length;
      visible.push(this.systems[index]);
    }
    return visible;
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'operational':
        return 'check_circle';
      case 'degraded':
        return 'warning';
      case 'outage':
        return 'error';
      case 'maintenance':
        return 'build';
      default:
        return 'help';
    }
  }

  getStatusClass(status: string): string {
    return `status-${status}`;
  }

  formatResponseTime(responseTime: number | null | undefined): string {
    if (responseTime === null || responseTime === undefined) return 'N/A';
    if (responseTime < 1000) return `${responseTime}ms`;
    return `${(responseTime / 1000).toFixed(1)}s`;
  }

  formatUptime(uptime: number): string {
    return `${uptime.toFixed(1)}%`;
  }

  formatLastUpdated(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins === 1) return '1 minute ago';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours === 1) return '1 hour ago';
    if (diffHours < 24) return `${diffHours} hours ago`;
    
    return date.toLocaleDateString();
  }

  onMouseEnter(): void {
    // Pause auto-scroll on hover
    this.destroy$.next();
  }

  onMouseLeave(): void {
    // Resume auto-scroll when not hovering
    this.startAutoScroll();
  }

  getOperationalCount(): number {
    return this.systems.filter(s => s.status === 'operational').length;
  }

  getIssuesCount(): number {
    return this.systems.filter(s => s.status === 'degraded' || s.status === 'outage').length;
  }
}
