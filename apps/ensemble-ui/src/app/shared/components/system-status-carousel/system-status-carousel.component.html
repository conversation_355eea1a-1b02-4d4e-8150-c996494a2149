<div class="system-status-carousel" 
     (mouseenter)="onMouseEnter()" 
     (mouseleave)="onMouseLeave()">
  
  <div class="carousel-header">
    <div class="header-content">
      <mat-icon class="status-icon">monitor_heart</mat-icon>
      <h3 class="carousel-title">System Status</h3>
      <div class="status-summary">
        <span class="operational-count">{{ getOperationalCount() }} operational</span>
        <span class="divider">•</span>
        <span class="issues-count" *ngIf="getIssuesCount() > 0">
          {{ getIssuesCount() }} issues
        </span>
        <span class="all-good" *ngIf="getIssuesCount() === 0">
          All systems operational
        </span>
      </div>
    </div>
  </div>

  <div class="carousel-container">
    <div class="carousel-track" 
         [style.transform]="'translateX(-' + (currentIndex * (100 / itemsToShow)) + '%)'">
      
      <div class="system-card" 
           *ngFor="let system of systems; let i = index"
           [class]="getStatusClass(system.status)">
        
        <div class="card-header">
          <div class="system-info">
            <mat-icon class="system-status-icon" [class]="getStatusClass(system.status)">
              {{ getStatusIcon(system.status) }}
            </mat-icon>
            <div class="system-name">{{ system.name }}</div>
          </div>
          <div class="status-badge" [class]="getStatusClass(system.status)">
            {{ system.status | titlecase }}
          </div>
        </div>

        <div class="card-content">
          <p class="system-description">{{ system.description }}</p>
          
          <div class="metrics" *ngIf="system.status !== 'maintenance'">
            <div class="metric" *ngIf="system.responseTime !== null">
              <span class="metric-label">Response:</span>
              <span class="metric-value">{{ formatResponseTime(system.responseTime) }}</span>
            </div>
            <div class="metric" *ngIf="system.uptime !== undefined">
              <span class="metric-label">Uptime:</span>
              <span class="metric-value">{{ formatUptime(system.uptime) }}</span>
            </div>
          </div>

          <div class="last-updated">
            <mat-icon class="update-icon">schedule</mat-icon>
            <span>{{ formatLastUpdated(system.lastUpdated) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="carousel-controls">
    <button class="control-btn prev" 
            (click)="previousSlide()" 
            aria-label="Previous systems">
      <mat-icon>chevron_left</mat-icon>
    </button>
    
    <div class="indicators">
      <div class="indicator" 
           *ngFor="let system of systems; let i = index"
           [class.active]="i === currentIndex"
           (click)="currentIndex = i">
      </div>
    </div>
    
    <button class="control-btn next" 
            (click)="nextSlide()" 
            aria-label="Next systems">
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>
</div>
