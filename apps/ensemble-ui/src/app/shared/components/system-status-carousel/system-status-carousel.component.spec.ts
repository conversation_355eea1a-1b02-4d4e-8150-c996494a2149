import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SystemStatusCarouselComponent } from './system-status-carousel.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('SystemStatusCarouselComponent', () => {
  let component: SystemStatusCarouselComponent;
  let fixture: ComponentFixture<SystemStatusCarouselComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SystemStatusCarouselComponent, NoopAnimationsModule]
    }).compileComponents();

    fixture = TestBed.createComponent(SystemStatusCarouselComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have mock system data', () => {
    expect(component.systems).toBeDefined();
    expect(component.systems.length).toBeGreaterThan(0);
  });

  it('should include all required systems', () => {
    const systemNames = component.systems.map(s => s.name);
    expect(systemNames).toContain('AMP');
    expect(systemNames).toContain('RLAPI');
    expect(systemNames).toContain('LIS');
    expect(systemNames).toContain('LOPA');
    expect(systemNames).toContain('OurHouse');
    expect(systemNames).toContain('AWS Route53');
    expect(systemNames).toContain('AWS EKS');
  });

  it('should count operational systems correctly', () => {
    const operationalSystems = component.systems.filter(s => s.status === 'operational');
    expect(operationalSystems.length).toBeGreaterThan(0);
  });

  it('should count systems with issues correctly', () => {
    const degradedSystems = component.systems.filter(s => s.status === 'degraded');
    const outageSystems = component.systems.filter(s => s.status === 'outage');
    const totalIssues = degradedSystems.length + outageSystems.length;
    expect(totalIssues).toBeGreaterThanOrEqual(0);
  });

  it('should navigate to next slide', () => {
    const initialIndex = component.currentIndex;
    component.nextSlide();
    expect(component.currentIndex).toBe((initialIndex + 1) % component.systems.length);
  });

  it('should navigate to previous slide', () => {
    component.currentIndex = 1;
    component.previousSlide();
    expect(component.currentIndex).toBe(0);
  });

  it('should wrap around when going to previous from first slide', () => {
    component.currentIndex = 0;
    component.previousSlide();
    expect(component.currentIndex).toBe(component.systems.length - 1);
  });

  it('should return correct number of visible systems', () => {
    const visibleSystems = component.getVisibleSystems();
    expect(visibleSystems.length).toBe(component.itemsToShow);
  });

  it('should format response time correctly', () => {
    expect(component.formatResponseTime(145)).toBe('145ms');
    expect(component.formatResponseTime(1500)).toBe('1.5s');
    expect(component.formatResponseTime(null)).toBe('N/A');
    expect(component.formatResponseTime(undefined)).toBe('N/A');
  });

  it('should format uptime correctly', () => {
    expect(component.formatUptime(99.9)).toBe('99.9%');
    expect(component.formatUptime(100)).toBe('100.0%');
  });

  it('should return correct status icon', () => {
    expect(component.getStatusIcon('operational')).toBe('check_circle');
    expect(component.getStatusIcon('degraded')).toBe('warning');
    expect(component.getStatusIcon('outage')).toBe('error');
    expect(component.getStatusIcon('maintenance')).toBe('build');
  });

  it('should return correct status class', () => {
    expect(component.getStatusClass('operational')).toBe('status-operational');
    expect(component.getStatusClass('degraded')).toBe('status-degraded');
  });

  it('should format last updated time correctly', () => {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
    const result = component.formatLastUpdated(oneMinuteAgo);
    expect(result).toBe('1 minute ago');
  });
});
