<div class="animated-loan-container">
  <!-- Hidden input for actual form control -->
  <input
    class="animated-input"
    [formControl]="inputControl"
    (input)="onInput($event)"
    (keydown)="onKeyDown($event)"
    [maxlength]="maxLength"
    autocomplete="off"
    inputmode="numeric"
    type="text">

  <!-- Visual display area -->
  <div class="display-area">
    <!-- Placeholder when empty -->
    <div class="placeholder" [class.hidden]="displayDigits.length > 0">
      {{ placeholder }}
    </div>

    <!-- Animated digits and placeholders -->
    <div class="digits-container">
      <!-- Actual digits -->
      <div
        *ngFor="let digit of displayDigits; let i = index; trackBy: trackByIndex"
        class="digit"
        [class.animating-in]="isDigitAnimating(i)"
        [class.animating-out]="!getDigitAtIndex(i) && isDigitAnimating(i)">
        {{ digit }}
      </div>

      <!-- Placeholder dots for remaining digits -->
      <div
        *ngFor="let placeholder of getPlaceholderDots(); let i = index; trackBy: trackByIndex"
        class="digit placeholder-dot"
        [class.visible]="shouldShowPlaceholders()">
        •
      </div>
    </div>
  </div>

  <!-- Bottom border line -->
  <div class="bottom-border"></div>

  <!-- Invalid input warning -->
  <div class="invalid-input-warning" [class.visible]="showInvalidInputWarning">
    <mat-icon>info</mat-icon>
    <span>Only numbers are allowed</span>
  </div>
</div>
