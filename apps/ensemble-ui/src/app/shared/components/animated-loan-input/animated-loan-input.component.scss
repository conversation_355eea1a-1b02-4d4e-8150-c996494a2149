.animated-loan-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  cursor: text;

  // Hidden input that handles the actual form control
  .animated-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    border: none;
    outline: none;
    background: transparent;
    font-size: 24px;
    z-index: 10;
    cursor: text;

  }

  // Visual display area
  .display-area {
    position: relative;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 8px;
    background: transparent;
  }

  // Placeholder text
  .placeholder {
    position: absolute;
    color: #9e9e9e;
    font-size: 18px;
    font-weight: 400;
    transition: opacity 0.3s ease;
    pointer-events: none;
    user-select: none;

    &.hidden {
      opacity: 0;
    }
  }

  // Container for animated digits
  .digits-container {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
    min-height: 32px;
  }

  // Individual digit styling
  .digit {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    min-width: 16px;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;

    // Animation for digits appearing
    &.animating-in {
      animation: digitSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    // Animation for digits disappearing
    &.animating-out {
      animation: digitSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    // Placeholder dots styling
    &.placeholder-dot {
      color: #bdbdbd;
      font-size: 20px;
      opacity: 0;
      transform: scale(0.8);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

      &.visible {
        opacity: 1;
        transform: scale(1);
        animation: placeholderFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  // Bottom border line
  .bottom-border {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #000 50%, transparent 100%);
    transform: scaleX(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  // Focus state
  &:focus-within {
    .bottom-border {
      transform: scaleX(1);
    }

    .placeholder {
      color: #000;
    }
  }

  // Hover state
  &:hover:not(:focus-within) {
    .bottom-border {
      transform: scaleX(0.3);
      background: linear-gradient(90deg, transparent 0%, #9e9e9e 50%, transparent 100%);
    }
  }

  // Invalid input warning
  .invalid-input-warning {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 8px;
    padding: 8px 12px;
    background: #de3341;
    color: white;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    white-space: nowrap;
    z-index: 20;

    &.visible {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    // Small arrow pointing up
    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-bottom: 4px solid #de3341;
    }
  }
}

// Keyframe animations
@keyframes digitSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.8);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-5px) scale(1.1);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes digitSlideOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translateY(5px) scale(1.1);
  }
  100% {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
}

@keyframes placeholderFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.6);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .animated-loan-container {
    max-width: 300px;

    .digit {
      font-size: 20px;
      min-width: 14px;
    }

    .placeholder {
      font-size: 16px;
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .animated-loan-container {
    .digit {
      color: #e0e0e0;

      &.placeholder-dot {
        color: #616161;
      }
    }

    .placeholder {
      color: #757575;

      &:focus-within {
        color: #de3341;
      }
    }

    .bottom-border {
      background: linear-gradient(90deg, transparent 0%, #de3341 50%, transparent 100%);
    }

    &:hover:not(:focus-within) {
      .bottom-border {
        background: linear-gradient(90deg, transparent 0%, #757575 50%, transparent 100%);
      }
    }
  }
}
