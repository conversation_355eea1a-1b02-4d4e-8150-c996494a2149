<div class="loan-search-container" [class.searching]="isSearching">
  <!-- Main Content Area -->
  <div class="main-content">
    <!-- Application Title -->
    <ens-animated-title title="Ensemble"></ens-animated-title>

    <!-- Centered Unified Search Form -->
    <ens-search-form
      [searchError]="searchError"
      [isSearching]="isSearching"
      (valueChange)="onAnimatedInputChange($event)"
      (enterPressed)="onAnimatedInputEnter($event)">
    </ens-search-form>

    <!-- Recent Searches -->
    <ens-recent-searches
      [recentSearches]="recentSearches"
      [formatLoanNumber]="formatLoanNumber.bind(this)"
      (selectSearch)="selectRecentSearch($event)"
      (removeSearch)="removeFromRecentWrapper($event)"
      (clearAll)="clearRecentSearches()">
    </ens-recent-searches>
  </div>

  <!-- Footer with System Status Carousel -->
  <footer class="page-footer">
    <ens-system-status-carousel></ens-system-status-carousel>
  </footer>
</div>
