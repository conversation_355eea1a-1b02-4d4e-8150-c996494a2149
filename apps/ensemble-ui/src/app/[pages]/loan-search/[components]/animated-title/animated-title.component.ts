import { Component, AfterViewInit, ElementRef, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'ens-animated-title',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="app-title">
      <h1>
        <span
          *ngFor="let letter of letters; trackBy: trackByIndex"
          class="letter"
          [attr.data-letter]="letter"
          [innerHTML]="letter === ' ' ? '&nbsp;' : '0'">
        </span>
      </h1>
      <p class="subtitle">Tools for Loans</p>
    </div>
  `,
  styleUrls: ['./animated-title.component.scss']
})
export class AnimatedTitleComponent implements AfterViewInit {
  private readonly elementRef = inject(ElementRef);

  @Input() title = 'Ensemble';
  @Input() animationDelay = 150; // Delay between each letter animation
  @Input() shuffleDuration = 800; // How long each letter shuffles

  letters: string[] = [];

  ngOnInit(): void {
    this.letters = this.title.split('');
  }

  ngAfterViewInit(): void {
    this.startTitleAnimation();
  }

  /**
   * Start the title letter shuffling animation
   */
  private startTitleAnimation(): void {
    const letterElements = this.elementRef.nativeElement.querySelectorAll('.letter');

    letterElements.forEach((element: HTMLElement, index: number) => {
      const targetLetter = element.getAttribute('data-letter') || '';
      const delay = index * this.animationDelay;

      // Start shuffling after initial delay
      setTimeout(() => {
        this.shuffleLetter(element, targetLetter, this.shuffleDuration);
      }, delay);
    });
  }

  /**
   * Shuffle a single letter element
   */
  private shuffleLetter(element: HTMLElement, targetLetter: string, duration: number): void {
    const numbers = '0123456789';
    const startTime = Date.now();
    const shuffleInterval = 50; // Change number every 50ms

    const shuffle = () => {
      const elapsed = Date.now() - startTime;
      const progress = elapsed / duration;

      if (progress < 1) {
        // Still shuffling - show random number
        const randomNumber = numbers[Math.floor(Math.random() * numbers.length)];
        element.textContent = randomNumber;

        // Continue shuffling
        setTimeout(shuffle, shuffleInterval);
      } else {
        // Animation complete - show target letter
        element.textContent = targetLetter;
        element.classList.add('settled');
      }
    };

    shuffle();
  }

  /**
   * TrackBy function for letters
   */
  trackByIndex(index: number): number {
    return index;
  }
}
