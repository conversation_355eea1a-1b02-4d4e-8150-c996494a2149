// Recent searches styles - moved from parent component
.recent-searches {
  max-width: 600px;
  margin: 0 auto;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.recent-searches-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  
  mat-icon {
    color: #666;
    font-size: 1.2rem;
    width: 1.2rem;
    height: 1.2rem;
  }
  
  span {
    flex: 1;
    font-weight: 500;
    color: #333;
  }
  
  .clear-all-button {
    color: #de3341; // RDS brand red
    font-size: 0.85rem;
    
    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      margin-right: 0.25rem;
    }
  }
}

.recent-search-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  
  &:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(222, 51, 65, 0.2); // RDS brand red with opacity
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:last-child {
    margin-bottom: 0;
  }
  
  mat-icon {
    color: #666;
    font-size: 1.1rem;
    width: 1.1rem;
    height: 1.1rem;
  }
  
  .loan-number {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-weight: 500;
    color: #333;
    letter-spacing: 0.05em;
  }
  
  .remove-button {
    opacity: 0;
    transition: opacity 0.2s ease;
    color: #999;
    
    &:hover {
      color: #de3341; // RDS brand red
    }
    
    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }
  }
  
  &:hover .remove-button {
    opacity: 1;
  }
}
