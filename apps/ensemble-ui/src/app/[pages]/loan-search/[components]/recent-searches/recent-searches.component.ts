import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'ens-recent-searches',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule
  ],
  template: `
    <div *ngIf="recentSearches.length > 0" class="recent-searches">
      <div class="recent-searches-header">
        <mat-icon>history</mat-icon>
        <span>Recent Searches</span>
        <button
          mat-button
          (click)="onClearAll()"
          class="clear-all-button">
          <mat-icon>clear_all</mat-icon>
          Clear All
        </button>
      </div>

      <div class="recent-search-item"
           *ngFor="let loanNumber of recentSearches; trackBy: trackByLoanNumber"
           (click)="onSelectSearch(loanNumber)">
        <mat-icon>search</mat-icon>
        <span class="loan-number">{{ formatLoanNumber ? formatLoanNumber(loanNumber) : loanNumber }}</span>
        <button
          mat-icon-button
          class="remove-button"
          (click)="onRemoveSearch(loanNumber, $event)">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./recent-searches.component.scss']
})
export class RecentSearchesComponent {
  @Input() recentSearches: string[] = [];
  @Input() formatLoanNumber: ((loanNumber: string) => string) | null = null;

  @Output() selectSearch = new EventEmitter<string>();
  @Output() removeSearch = new EventEmitter<string>();
  @Output() clearAll = new EventEmitter<void>();

  onSelectSearch(loanNumber: string): void {
    this.selectSearch.emit(loanNumber);
  }

  onRemoveSearch(loanNumber: string, event: Event): void {
    event.stopPropagation(); // Prevent triggering the search
    this.removeSearch.emit(loanNumber);
  }

  onClearAll(): void {
    this.clearAll.emit();
  }

  /**
   * TrackBy function for recent searches list
   */
  trackByLoanNumber(_index: number, loanNumber: string): string {
    return loanNumber;
  }
}
