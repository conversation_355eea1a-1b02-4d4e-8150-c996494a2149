// Search form styles - moved from parent component
.search-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  width: 100%;
  max-width: 600px;
  margin-bottom: 2rem;
}

.search-input-container {
  position: relative;
  width: 100%;
  max-width: 480px;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 16px;

  // Animated input styling
  ens-animated-loan-input {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
  }
}

.message-area {
  min-height: 80px; // Increased from 48px to 80px for more space
  display: flex;
  align-items: flex-start;
  justify-content: center;
  transition: all 0.3s ease;
  margin-top: 16px; // Add some top margin for better spacing
  width: 100%;
}

.search-error {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 1rem;
  padding: 12px 20px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 20px;
  color: #d32f2f;
  font-size: 14px;
  width: 100%;
  max-width: 480px;
  animation: slideIn 0.3s ease-out;

  mat-icon {
    color: #d32f2f;
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #1976d2;
  font-size: 14px;
  font-weight: 500;
  animation: fadeIn 0.3s ease-out;

  span {
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
