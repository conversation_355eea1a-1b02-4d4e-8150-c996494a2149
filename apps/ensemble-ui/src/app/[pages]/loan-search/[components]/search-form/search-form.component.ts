import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { AnimatedLoanInputComponent } from '../../../../shared/components/animated-loan-input/animated-loan-input.component';

@Component({
  selector: 'ens-search-form',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    AnimatedLoanInputComponent
  ],
  template: `
    <div class="search-form">
      <div class="search-input-container">
        <ens-animated-loan-input
          placeholder="Enter 10-digit loan number"
          [maxLength]="10"
          [showPlaceholdersAfter]="1"
          (valueChange)="onValueChange($event)"
          (enterPressed)="onEnterPressed($event)">
        </ens-animated-loan-input>

        <!-- Reserved space for error/warning messages -->
        <div class="message-area">
          <div *ngIf="searchError" class="search-error">
            <mat-icon>error</mat-icon>
            <span>{{ searchError }}</span>
          </div>
          <div *ngIf="isSearching" class="search-loading">
            <span>Searching...</span>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./search-form.component.scss']
})
export class SearchFormComponent {
  @Input() searchError: string | null = null;
  @Input() isSearching = false;

  @Output() valueChange = new EventEmitter<string>();
  @Output() enterPressed = new EventEmitter<string>();

  onValueChange(value: string): void {
    this.valueChange.emit(value);
  }

  onEnterPressed(value: string): void {
    this.enterPressed.emit(value);
  }
}
