// Centered within content area
.loan-search-container {
  display: flex;
  flex-direction: column;
  // Calculate available height: 100vh - header (48px + 32px padding) - page content padding (40px)
  min-height: calc(100vh - 120px);
  padding: 0; // Remove padding to use full content area
  margin: -20px; // Offset the page-content padding to use full area
  background: transparent;
  position: relative;
  transition: opacity 0.3s ease;



  &.searching {
    .app-title,
    .search-form,
    .recent-searches,
    .search-tips {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// Main content area that takes up available space
.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1; // Take up remaining space above footer
  min-height: 0; // Allow flex shrinking
}

// Footer positioned at bottom
.page-footer {
  margin-top: auto; // Push to bottom
  width: 100%;
  padding: 0;

  // Override carousel component's margin-top
  ens-system-status-carousel {
    ::ng-deep .system-status-carousel {
      margin-top: 0;
    }
  }
}

// Component-specific styling for proper layout
ens-animated-title {
  position: absolute;
  top: 20vh; // Position to be between header and search input
  left: 50%;
  transform: translateX(-50%); // Center horizontally
  z-index: 1; // Ensure it's above other content
  animation: titleFadeIn 1.2s ease-out;
}

ens-search-form {
  display: flex;
  width: 100%;
  max-width: 600px;
  justify-content: center;
}

ens-recent-searches {
  width: 100%;
  max-width: 600px;
  display: flex;
  justify-content: center;
}

// Title animations
@keyframes titleFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Responsive design
@media (max-width: 768px) {
  .loan-search-container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  ens-animated-title {
    top: 15vh; // Adjust position for mobile to maintain proportional spacing
  }

  ens-search-form {
    max-width: 100%;
  }

  ens-recent-searches {
    max-width: 100%;
  }
}



// Animations are now handled by individual components
