<div class="app-container">
  <!-- Left Menu - Full viewport height with hover expansion -->
  <nav class="sidebar" [class.visible]="sideMenuOpen" [class.expanded]="sideMenuExpanded"
       (mouseenter)="onSideMenuMouseEnter()" (mouseleave)="onSideMenuMouseLeave()">
    <div class="sidebar-content">
      <div class="nav-item" (click)="navigateToLoanSearch()">
        <mat-icon>search</mat-icon>
        <span class="nav-text">Loan Search</span>
      </div>

      <div class="nav-item" (click)="navigateToLoanStatus()">
        <mat-icon>assignment</mat-icon>
        <span class="nav-text">Loan Status</span>
      </div>

      <div class="nav-item" (click)="navigateToLoanTrace()">
        <mat-icon>timeline</mat-icon>
        <span class="nav-text">Loan Trace</span>
      </div>

      <div class="nav-item" (click)="navigateToLogs()">
        <mat-icon>description</mat-icon>
        <span class="nav-text">Logs</span>
      </div>
    </div>
  </nav>

  <!-- Main Content with Header Component -->
  <main class="main-content" [class.shifted]="sideMenuOpen" [class.expanded]="sideMenuExpanded">
    <!-- Header Component at top of content -->
    <header class="content-header">
      <button
        mat-icon-button
        class="menu-btn"
        (click)="sideMenuOpen ? closeSideMenu() : openSideMenu()"
      >
        <mat-icon>menu</mat-icon>
      </button>

      <div class="loan-display">
        <ng-content select="[slot=header-content]"></ng-content>
      </div>

      <div class="user-menu"
           [class.expanded]="profileMenuExpanded || isProfileMenuOpen"
           [class.circular]="!profileMenuExpanded && !isProfileMenuOpen"
           (mouseenter)="onProfileMenuMouseEnter()"
           (mouseleave)="onProfileMenuMouseLeave()">
        <button
          [matMenuTriggerFor]="accountMenu"
          class="profile-btn"
          (click)="toggleProfileMenu()"
          (menuOpened)="onMenuOpened()"
          (menuClosed)="onMenuClosed()"
        >
          <div class="profile-content">
            <div class="user-avatar">{{ user.avatar }}</div>
            <div class="user-details">
              <div class="user-name">{{ user.name }}</div>
              <div class="user-role">{{ user.role }}</div>
            </div>
            <mat-icon class="dropdown-icon">keyboard_arrow_down</mat-icon>
          </div>
        </button>

        <mat-menu #accountMenu="matMenu" class="profile-dropdown">
          <button mat-menu-item (click)="openProfile()">
            <mat-icon>person</mat-icon>
            <span>View Profile</span>
          </button>
          <button mat-menu-item (click)="openSettings()">
            <mat-icon>settings</mat-icon>
            <span>Account Settings</span>
          </button>
          <button mat-menu-item (click)="openNotifications()">
            <mat-icon>notifications</mat-icon>
            <span>Notifications</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="openHelp()">
            <mat-icon>help_outline</mat-icon>
            <span>Help & Support</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()" class="logout-item">
            <mat-icon>logout</mat-icon>
            <span>Sign Out</span>
          </button>
        </mat-menu>
      </div>
    </header>

    <!-- Page Content -->
    <div class="page-content">
      <ng-content></ng-content>
    </div>
  </main>
</div>
